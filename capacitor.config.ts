import type { CapacitorConfig } from '@capacitor/cli';

// eslint-disable-next-line node/prefer-global/process
const isProduction = process.env.NODE_ENV === 'production';
const channel = 'W2ATEST';

const productionUrl = `https://g9.game?source=${channel}`;
const stagingUrl = `https://img.g9aaa.com/?source=${channel}`;
// const testUrl = 'http://192.168.2.222:5001/';
const config: CapacitorConfig = {
  appId: 'com.g9game.app',
  appName: 'g9Game',
  webDir: 'dist',
  server: {
    url: isProduction ? productionUrl : stagingUrl,
    // url: testUrl,
    cleartext: !isProduction, // 允许明文流量
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 0,
    },
  },
};

export default config;
