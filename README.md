## 屏幕宽度断点

- mobiles：360px
- mobile：480px
- mobilel：600px
- tablet：768px（常用，小于它算手机）
- tabletl：1024px
- laptop：1280px（常用，小于它算平板）
- laptopl：1440px
- desktop：1600px

## 容器宽度断点

参考 tailwindcss-container-queries 插件的断点配置
相关: useElementBreakpoint

- xs: 320
- sm: 384
- md: 448
- lg: 512
- xl: 576
- 2xl: 672
- 3xl: 768
- 4xl: 896
- 5xl: 1024
- 6xl: 1152
- 7xl: 1280

## 自定义的 Vue Properties

为了在视图中方便使用常用的全局数据，所以注入了以下 Properties：

1. $bp：屏幕断点数据

```
$bp.tablet; // 大于 tablet 的尺寸
$bp.ltTablet; // 小于 tablet 的尺寸
$bp.atTablet; // 大于 tablet 并 小于 tabletl 的尺寸
```

2. $store：stores 目录中的各个 store

## 组件的存放位置

组件按照使用范围可分为

- 全局组件
  - 公共 components。公共组件不应该包含业务逻辑或依赖store中的数据
  - 业务 components-business
- 模块组件 pages/xxx/components
- 页面组件 pages/xxx/xxx/components

# unocss 写法备忘

- `media-[hover:hover]:hover:text-white`: 只有当设备支持hover时，才使用hover样式
- `lang-zh:text-red`: 当页面是中文时，字体为红色

# [Ton图标](https://ton.org/brand-assets)

# 安卓配置

## [生成图标资源](https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html)

> 替换resources->icon.png(1024\*1024)文件和resources->android->icons下的所有文件
> 执行命令 `pnpm generate:icon`

## 打包安卓应用

> 安装javaSDK，配置`JAVA_HOME`环境变量，配置`ALONE_FRONTEND_SIGNING_PWD`和`ALONE_FRONTEND_SIGNING_ALIAS`到用户环境变量，值分别是证书密码和证书别名
>
> 执行命令 `pnpm build:android`(生产)

## 调试

> `pnpm build:android:staging`(测试)
> 安装Android studio 进行调试
> 安装adb，调试指令：adb.exe logcat | grep -iE "FCM|FCMService"
