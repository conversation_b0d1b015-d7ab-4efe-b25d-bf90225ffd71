{"name": "game-web", "type": "module", "version": "2.0.0", "private": true, "scripts": {"dev": "vite --host --force", "build": "vite build", "build:staging": "vite build --mode staging", "build:android": "set NODE_ENV=production && vite build && npx cap sync", "build:android:staging": "npx cap sync", "build:svn": "tsx scripts/svn.ts", "generate:icon": "npx capacitor-assets generate --android", "preview": "vite preview", "api": "npx ts-codegen", "proto-to-ts": "tsx scripts/protoToTs.ts", "lint": "eslint .", "lint:fix": "eslint . --fix -q", "debug": "node --inspect-brk src/index.js"}, "dependencies": {"@adjustcom/adjust-web-sdk": "^5.7.2", "@capacitor/push-notifications": "^7.0.1", "@intercom/messenger-js-sdk": "^0.0.14", "@peng_kai/kit": "0.3.0-beta.8", "@sentry/vue": "^8.33.1", "ant-design-vue": "^4.2.5", "clsx": "^2.1.1", "gsap": "^3.12.7", "lottie-web": "^5.12.2", "motion-v": "^0.13.1", "protobufjs": "^7.4.0", "video.js": "^8.19.1", "viewerjs": "^1.11.7", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@capacitor/android": "^7.1.0", "@capacitor/app": "^7.0.0", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.1.0", "@capacitor/core": "^7.2.0", "@intlify/unplugin-vue-i18n": "^6.0.4", "@jhqn/stylelint-config": "^0.6.15", "@peng_kai/theme": "^0.0.18", "@sentry/vite-plugin": "^2.22.5", "@tonconnect/ui": "2.0.9", "@ts-tool/ts-codegen-cli": "^3.3.4", "@types/lodash-es": "^4.17.12", "@types/node": "^20.16.2", "@unocss/eslint-plugin": "0.63.4", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/tsconfig": "^0.5.1", "archiver": "^7.0.1", "autoprefixer": "^10.4.20", "eslint": "^9.12.0", "eslint-plugin-format": "^0.1.2", "eslint-plugin-prettier": "^5.2.1", "postcss-preset-env": "^10.0.6", "protobufjs-cli": "^1.1.3", "rollup-plugin-external-globals": "^0.12.0", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.79.4", "stylelint": "^16.9.0", "telegram-webapps": "^7.10.0", "type-fest": "^4.26.1", "typescript": "^5.6.3", "unocss": "0.63.4", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.8", "vite-plugin-html": "^3.2.2", "vite-plugin-ts-alias": "^0.1.1", "vite-plugin-vue-devtools": "^7.4.6", "vue-component-type-helpers": "^2.1.6", "vue-i18n": "^11.1.2", "vue-tsc": "^2.1.6"}, "eslintIgnore": ["build/", "dist/", "public/", "pnpm-lock.yaml", "**/*.test.js"], "browserslist": ["Chrome >= 70", "Safari >= 14", "Firefox >= 68"], "pnpm": {"patchedDependencies": {"ant-design-vue@4.2.5": "patches/<EMAIL>"}}}