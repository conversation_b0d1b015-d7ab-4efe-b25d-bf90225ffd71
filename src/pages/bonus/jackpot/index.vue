<script setup lang="ts">
import { watchDeep } from '@peng_kai/kit/libs/vueuse';
import JackpotHeader from '~/pages/bonus/jackpot/components/JackpotHeader.vue';
import JackpotHistory from '~/pages/bonus/jackpot/components/JackpotHistory.vue';
import { jackpotReceive } from '~/pages/bonus/jackpot/components/JackpotReceive.vue';
import JackpotRules from '~/pages/bonus/jackpot/components/JackpotRules.vue';
import { useJackpotData } from './useJackpotData';

const { subjectLiveList, subjectLive, subjectLiveTopThree } = useJackpotData();
const bp = useAppBreakpoints();

/**
 * 历史记录模态框
 */
const history = useAntdModal(
  JackpotHistory,
  {
    title: computed(() => $t('fWgSgW1tklRsPc5ObejY')),
    wrapClassName: 'ant-cover__Modal-drawer [--min-modal-height:70vh]',
    centered: true,
    footer: null,
  },
);

/**
 * 规则说明模态框
 */
const rules = useAntdModal(
  JackpotRules,
  {
    title: computed(() => $t('unpR6nvGh7xewejZ8RMr2')),
    wrapClassName: 'ant-cover__Modal-drawer [--min-modal-height:90vh]',
    centered: true,
    footer: null,
  },
);

/**
 * 我的排名信息
 */
const myRankInfo = computed(() => {
  return subjectLive.value?.my_rank;
});

/**
 * 我的排名显示文本
 */
const myRankDisplay = computed((): string => {
  const rankNo = myRankInfo.value?.rank_no;
  return rankNo ? String(rankNo) : 'No Rank';
});

// ============= 响应式监听 =============

/**
 * 监听断点变化，在平板模式下自动关闭模态框
 */
watchDeep(bp, () => {
  if (bp.tablet) {
    history.close?.();
    rules.close?.();
  }
});
</script>

<template>
  <div class="jackpot">
    <JackpotHeader @onReceive="jackpotReceive.open?.();" />
    <!-- router -->
    <div class="jackpot-router">
      <div @click="rules?.open?.()">
        {{ $t('unpR6nvGh7xewejZ8RMr2') }}
      </div>
      <div @click="history?.open?.()">
        {{ $t('fWgSgW1tklRsPc5ObejY') }}
      </div>
    </div>
    <!-- subject -->
    <div class="jackpot-subject">
      <!-- 排行 -->
      <div class="jackpot-subject-ranking">
        <div class="jackpot-subject-ranking_top mb-10 lt-tablet:(mx--5 mb-0)">
          <img src="../imgs/jackpot-ranking.png" class="object-cover">
          <div class="copy lt-tablet:h-100%">
            <div
              v-for="(item, k) in subjectLiveTopThree"
              :key="k"
              class="mb-2 lt-tablet:(mb-0 text-xs)"
              :class="{ 'mb-6': k === 1, 'mb-1': k === 2 }"
            >
              {{ item?.user?.nickname }}
              <br>
              <TNum :value="item?.bet_amount ?? '0'" :decimals="2" format="fixed-dec" />
            </div>
          </div>
        </div>
        <div class="twins-table">
          <div class="twins-table-th">
            <div class="twins-table_title">
              <span class="text-left">{{ $t('f785G9ekgsXLvsdTgXycD') }}</span>
              <span>{{ $t('djJh3HsXqzios2duy3dwZ') }}</span>
              <span class="text-right">{{ $t('mHr0AtKezgd1PQecos_5') }}</span>
            </div>
            <div class="twins-table_title">
              <span class="text-left">{{ $t('f785G9ekgsXLvsdTgXycD') }}</span>
              <span>{{ $t('djJh3HsXqzios2duy3dwZ') }}</span>
              <span class="text-right">{{ $t('mHr0AtKezgd1PQecos_5') }}</span>
            </div>
          </div>
          <div class="twins-table_content">
            <div v-if="bp.tablet">
              <template v-for="(item, k) of subjectLiveList[1]" :key="k">
                <div v-if="item.rank_no > 3" class="twins-table_content_tr">
                  <div class="text-left">
                    {{ item.rank_no }}
                  </div>
                  <div>{{ item.user?.nickname }}</div>
                  <div class="text-right">
                    <TNum :value="item.bet_amount" :decimals="2" format="fixed-dec" />
                  </div>
                </div>
              </template>
            </div>
            <div>
              <template v-for="(item, k) of subjectLiveList[0]" :key="k">
                <div v-if="item.rank_no > 3" class="twins-table_content_tr">
                  <div class="text-left">
                    {{ item.rank_no }}
                  </div>
                  <div>{{ item.user?.nickname }}</div>
                  <div class="text-right">
                    <TNum :value="item.bet_amount" :decimals="2" format="fixed-dec" />
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="divider-v-gradual h-auto" />
        </div>
        <div class="aggregate">
          <div class="aggregate-left">
            {{ myRankDisplay }}
          </div>
          <div class="divider-v-gradual" />
          <div class="aggregate-center">
            <p>{{ $t('adhvImOcUr4VUseAaSxyc') }} {{ myRankInfo?.bet_amount ?? '0' }}</p>
            <p>
              {{ $t('qz6xJ6MGSnFdJtCa4Klk') }}
              <TNum :value="((myRankInfo?.reward_rate ?? 0) * 100)" :decimals="2" format="original" />
              %
            </p>
          </div>
          <div class="divider-v-gradual" />
          <div class="aggregate-rigth">
            <p>{{ $t('xVw5NkAnQe74Z2Tg7rXq') }}</p>
            <p>{{ myRankInfo?.bet_amount_need ?? '0' }}</p>
          </div>
        </div>
      </div>
      <template v-if="bp.tablet">
        <JackpotHistory />
        <JackpotRules />
      </template>
    </div>

    <history.PresetComponent />
    <rules.PresetComponent />
    <jackpotReceive.PresetComponent />
  </div>
</template>

<style>
@import 'style.scss';
</style>

<style lang="scss" scoped>
.jackpot-subject-ranking {
  .jackpot-subject-ranking_top {
    --uno: 'relative inline-block self-center max-w-auto';

    img {
      width: clamp(350px, 80.583vw, 860px);
    }

    .copy {
      --uno: 'absolute w-full h-[calc(100%_-_0.4vw)] flex justify-around top-0 left-0 items-end text-14px lt-tablet:text-xs';
    }
  }

  @media bp-lt-tablet {
    --uno: 'mt-0';
  }
}
</style>
