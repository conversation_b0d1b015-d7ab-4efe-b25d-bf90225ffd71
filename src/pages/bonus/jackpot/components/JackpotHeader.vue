<script setup lang="ts">
import type { JackpotHeaderEmits } from '../types';
import { useJackpotData } from '../useJackpotData';

// ============= 类型定义 =============

interface SegmentedOption {
  value: number;
  label: string;
}

// ============= 组件定义 =============

const emits = defineEmits<JackpotHeaderEmits>();

// ============= 数据获取 =============

const { subjectLive, queryParams } = useJackpotData();

// ============= 计算属性 =============

/**
 * 分段控制器选项列表
 */
const typeList = computed((): SegmentedOption[] => [
  {
    value: 1,
    label: $t('t6mjY5u1mSnnpAZcDnfL1'),
  },
  {
    value: 2,
    label: $t('tYjLvE0sIvFU0lIcXyt'),
  },
]);

/**
 * 是否可以领取奖励
 */
const canClaim = computed((): boolean => {
  return (subjectLive.value?.my_rank?.can_claim ?? 0) === 1;
});

/**
 * 当前奖池金额
 */
const jackpotAmount = computed((): string => {
  return subjectLive.value?.jackpot_amount ?? '0';
});

// ============= 事件处理 =============

/**
 * 处理领取按钮点击
 */
function handleReceiveClick(): void {
  if (canClaim.value) {
    emits('onReceive');
  }
}
</script>

<template>
  <!-- Banner -->
  <div
    class="jackpot-header"
  >
    <!-- 切换 todo 以免zw又要 -->
    <div v-if="false" class="segmented">
      <ASegmented
        v-model:value="queryParams"
        class="w-full text-base"
        block
        :options="typeList"
      />
    </div>
    <div class="jackpot-header_number">
      <div class="title">
        <div class="jackpot-text">
          {{ $t('fbUzg2DfycRbZh0M37BwT') }}
        </div>
        <div class="subtitle-text">
          {{ $t('w0c8Ogp18olrLygcGMqHr') }}
        </div>
      </div>
      <div class="jackpot-amount">
        {{ jackpotAmount }}
      </div>
      <div
        class="bit"
        :class="{ active: canClaim }"
        @click="handleReceiveClick"
      >
        {{ $t('kaQIMrTfEjnMoIOwQ6U7') }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.jackpot-header {
  --uno: 'relative z-0 w-full pb-29%';

  &::before {
    content: '';
    --uno: 'abs-full -z-1';
    background: url('../../imgs/jackpot-banner.png') 50% / cover no-repeat;
  }

  .segmented {
    --uno: 'flex-center px-35%';
  }
  .jackpot-header_number {
    --uno: 'absolute bottom-30 left-50% flex flex-col translate-y-1/2 items-center gap-4 -translate-x-1/2';
    .title {
      --uno: 'text-center text-12 not-italic bg-clip-text font-900 leading-13.5 uppercase bg-gradient-to-b from-[#fef356] to-[#fda323] ';
      font-family: fantasy;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      filter: drop-shadow(0px 4px 0px rgba(0, 0, 0, 0.4));

      .jackpot-text {
        -webkit-text-stroke: 2px #a91919;
      }
      .subtitle-text {
        -webkit-text-stroke: 2px #420a0a;
      }
    }

    .jackpot-amount {
      --uno: 'border-1px border-[#FDB12C] rounded-full border-solid bg-[rgba(19,20,22,0.80)] min-w-180px px-8.75 text-3xl text-white font-bold flex-center min-h-60px';
      box-shadow: 0px 0px 10px 0px #fbb429;
    }

    .bit {
      --uno: 'flex-center rounded-lg px-19.3 py-2.5 text-6 text-white font-600 cursor-not-allowed lt-tablet:(pt-2 pb-2) ';
      background:
        linear-gradient(
          86deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0) 49.99%,
          rgba(255, 255, 255, 0.1) 50%,
          rgba(255, 255, 255, 0.1) 100%
        ),
        linear-gradient(180deg, var(--sys-text-color-sec, #72768d) 0%, var(--sys-layer-e, #3e404c) 100%);
      box-shadow: 0px -4px 0px 0px rgba(0, 0, 0, 0.25) inset;
      &.active {
        cursor: pointer;
        box-shadow: 0px -4px 0px 0px rgba(0, 0, 0, 0.25) inset;
        background:
          linear-gradient(
            86deg,
            rgba(255, 224, 2, 0) 0%,
            rgba(255, 224, 2, 0) 49.99%,
            rgba(255, 224, 2, 0.1) 50%,
            rgba(255, 224, 2, 0.1) 100%
          ),
          linear-gradient(180deg, #22c55e 0%, #14532d);
      }
    }
  }

  @media bp-lt-tabletl {
    --uno: 'pb-69%';
    &::before {
      background: url('../../imgs/jackpot-banner-mp.png') 50% / cover no-repeat;
      transform: scaleX(1.05);
    }
    .segmented {
      --uno: 'p-0 text-sm';
    }
    .jackpot-header_number {
      --uno: 'bottom-20';
      .title {
        --uno: 'text-6 leading-7';
      }
      .jackpot-title-img {
        --uno: 'h-56px';
      }
      .jackpot-amount {
        --uno: 'py-1 text-base min-h-auto';
      }
      .bit {
        --uno: 'text-base px-13.75';
      }
    }
  }
}
</style>
