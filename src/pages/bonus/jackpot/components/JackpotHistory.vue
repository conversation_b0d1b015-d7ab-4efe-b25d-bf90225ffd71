<script setup lang="ts">
import type { RankingItem } from '../types'
import { cols, RANKING_ICONS, useJackpotData } from '../useJackpotData'

// ============= 数据获取 =============

const { subjectHistoryList, subjectHistory } = useJackpotData()

// ============= 滚动同步逻辑 =============

const leftTableContainer = ref<HTMLElement>()
const rightTableContainer = ref<HTMLElement>()

/** 滚动状态标志，防止循环触发 */
let isScrolling = false

/**
 * 同步滚动函数
 * 确保左右表格同步滚动
 *
 * @param sourceElement 触发滚动的元素
 * @param targetElement 需要同步滚动的目标元素
 */
const syncScroll = (sourceElement: HTMLElement, targetElement: HTMLElement): void => {
  if (isScrolling) return

  isScrolling = true
  targetElement.scrollTop = sourceElement.scrollTop

  // 使用 requestAnimationFrame 确保滚动完成后重置标志
  requestAnimationFrame(() => {
    isScrolling = false
  })
}

// ============= 计算属性 =============

/**
 * 历史奖池金额
 */
const historyJackpotAmount = computed((): string => {
  return subjectHistory.value?.jackpot_amount ?? '0'
})

/**
 * 我的排名信息
 */
const myRankInfo = computed(() => {
  return subjectHistory.value?.my_rank
})

/**
 * 我的排名显示文本
 */
const myRankDisplay = computed((): string => {
  const rankNo = myRankInfo.value?.rank_no
  return rankNo ? String(rankNo) : 'No Rank'
})

/**
 * 获取排行榜图标
 */
const getRankIcon = (rank: number): string | null => {
  return rank <= 3 ? RANKING_ICONS[rank - 1] : null
}

// ============= 生命周期管理 =============

/**
 * 设置滚动同步监听器
 */
onMounted(async () => {
  await nextTick()

  const leftEl = leftTableContainer.value
  const rightEl = rightTableContainer.value

  if (!leftEl || !rightEl) return

  const leftScrollHandler = () => syncScroll(leftEl, rightEl)
  const rightScrollHandler = () => syncScroll(rightEl, leftEl)

  leftEl.addEventListener('scroll', leftScrollHandler)
  rightEl.addEventListener('scroll', rightScrollHandler)

  // 清理函数
  onUnmounted(() => {
    leftEl.removeEventListener('scroll', leftScrollHandler)
    rightEl.removeEventListener('scroll', rightScrollHandler)
  })
})
</script>

<template>
  <!-- history -->
  <div class="jackpot-subject-history">
    <p class="title lt-tablet:hidden">
      {{ $t('fWgSgW1tklRsPc5ObejY') }}
    </p>
    <div class="rounded-lg bg-sys-layer-a px-7.5 py-3.5 text-center text-sys-text-body lt-tablet:(px-0 py-3)">
      <p class="text-base font-normal">
        {{ $t('yzjLprsGxUY44LvISxd9') }}
      </p>
      <h5 class="mb-0 text-5 text-#18A349 font-normal">
        <TNum :value="historyJackpotAmount" :decimals="2" format="original" />
      </h5>
    </div>
    <div class="twins-table">
      <div class="twins-table-th">
        <div class="twins-table_title">
              <span class="text-left">{{ $t('f785G9ekgsXLvsdTgXycD') }}</span>
              <span>{{ $t('djJh3HsXqzios2duy3dwZ') }}</span>
              <span class="text-right">{{ $t('mHr0AtKezgd1PQecos_5') }}</span>
            </div>
        <div class="twins-table_title">
              <span class="text-left">{{ $t('f785G9ekgsXLvsdTgXycD') }}</span>
              <span>{{ $t('djJh3HsXqzios2duy3dwZ') }}</span>
              <span class="text-right">{{ $t('mHr0AtKezgd1PQecos_5') }}</span>
            </div>
      </div>
      <div class="twins-table_content">
        <div v-for="(item, key) in cols" :key>
          <div v-for="(item, k) of subjectHistoryList[key]" :key="k" class="twins-table_content_tr">
            <div class="text-left">
              <img
                v-if="getRankIcon(item.rank_no)"
                :src="getRankIcon(item.rank_no)!"
                alt=""
                class="w-4 object-cover"
              >
              <span v-else>{{ item.rank_no }}</span>
            </div>
            <div>{{ item.user?.nickname }}</div>
            <div class="text-right">
              <TNum :value="item.bet_amount" :decimals="2" format="fixed-dec" />
            </div>
          </div>
        </div>
      </div>
      <div class="divider-v-gradual h-auto" />
    </div>

    <div class="aggregate">
      <div class="aggregate-left">
        {{ myRankDisplay }}
      </div>
      <div class="divider-v-gradual" />
      <div class="aggregate-center">
        <p>{{ $t('adhvImOcUr4VUseAaSxyc') }} {{ myRankInfo?.bet_amount ?? 0 }}</p>
        <p>
          {{ $t('qz6xJ6MGSnFdJtCa4Klk') }}
          <TNum :value="((myRankInfo?.reward_rate ?? 0) * 100)" :decimals="2" format="original" />
          %
        </p>
      </div>
      <div class="divider-v-gradual" />
      <div class="aggregate-rigth">
        <p>{{ $t('xVw5NkAnQe74Z2Tg7rXq') }}</p>
        <p>{{ myRankInfo?.bet_amount_need ?? 0 }}</p>
      </div>
    </div>
  </div>
</template>

<style>
@import '../style.scss';
</style>
