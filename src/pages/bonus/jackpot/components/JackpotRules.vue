<script setup lang="ts">
import { RANKING_ICONS, useJackpotData } from '../useJackpotData';

// ============= 数据获取 =============

const { rulesConfigData } = useJackpotData();

// ============= 计算属性 =============

/**
 * 活动规则文本
 */
const activityRules = computed((): string[] => [
  $t('bzFxhWGi3qdkgQBjT2wmB'),
  $t('mpl9B0aumdG6KUpyRwn3'),
  $t('hETdxLgcnwqyzdjoyu810'),
  $t('nn0witw6unL7KdxbJ5G5'),
]);

/**
 * 条款规则文本
 */
const termsRules = computed((): string[] => [
  $t('wUmfAp1Mq7IpHgfXaxzo'),
  $t('fkVnkJvJ131m6GrBsL'),
  $t('nJ1Lm5QayVtE5Up7fRibc'),
]);

/**
 * 获取排行榜图标
 */
function getRankIcon(rank: number): string | null {
  return rank <= 3 ? RANKING_ICONS[rank - 1] : null;
}

/**
 * 格式化奖励比例
 */
function formatRewardRate(rate: string): number {
  return (Number.parseFloat(rate) || 0) * 100;
}
</script>

<template>
  <!-- Rules -->
  <div class="jackpot-subject-rules">
    <p class="title lt-tablet:hidden">
      {{ $t('unpR6nvGh7xewejZ8RMr2') }}
    </p>
    <div class="twins-table grid grid-cols-3 !gap-15">
      <div class="min-w-90 pr-1 lt-tablet:min-w-auto">
        <div class="flex-between text-sys-text-body">
          <span class="text-left">{{ $t('f785G9ekgsXLvsdTgXycD') }}</span>
          <span class="text-right">{{ $t('rrMtOCyqbu1vh69xDa61V') }}</span>
        </div>
        <div class="twins-table-main grid grid-cols-2 max-h-460px overflow-y-auto pr-1 lt-tablet:max-h-360px">
          <template v-for="(rule, k) of rulesConfigData" :key="k">
            <div class="py-2 text-left">
              <img
                v-if="getRankIcon(Number(rule.rank))"
                :src="getRankIcon(Number(rule.rank))!"
                alt=""
                class="w-4 object-cover"
              >
              <span v-else>{{ rule.rank }}</span>
            </div>
            <div class="py-2 text-right">
              <TNum :value="formatRewardRate(rule.reward_rate)" :decimals="2" format="pad-dec" /> %
            </div>
          </template>
        </div>
      </div>
      <div class="divider-v-gradual mx-0 h-auto !relative" />
      <div class="text-left">
        <div class="text-center text-24px text-white font-900 font-normal tablet:hidden">
          {{ $t('aRy00Td7Uhw2YcdavhcK8') }}
        </div>
        <div class="rule-item">
          <h4 class="rule-item_title">
            {{ $t('onLunbCiXInyBkrnRjH') }}
          </h4>

          <div v-for="(rule, k) in activityRules" :key="k" class="rule-item_content">
            {{ `${k + 1}. ${rule}` }}
          </div>
        </div>
        <div class="rule-item">
          <h4 class="rule-item_title">
            {{ $t('eVIkyjGkMuCbArDjq9J') }}
          </h4>
          <div v-for="(rule, k) in termsRules" :key="k" class="rule-item_content">
            {{ `${k + 1}. ${rule}` }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
@import '../style.scss';
</style>

<style scoped lang="scss">
.jackpot-subject-rules {
  .twins-table {
    --uno: 'grid grid-cols-[auto_auto_1fr] w-full items-stretch gap-10';
  }
  @media bp-lt-tablet {
    .twins-table {
      --uno: 'grid-cols-1';
    }
  }
  .rule-item {
    --uno: 'mt-5.5 text-4  text-sys-text-body font-600 font-normal leading-normal lt-tablet:text-sm';
    .rule-item_title {
      --uno: 'mb-2.5 text-sys-text-head ';
    }
    .rule-item_content {
      --uno: 'px-2 py-0.5';
      text-indent: -1.4em; /* 首行负缩进 */
      padding-left: 1.6em; /* 左边距补偿 */
      line-height: 1.6; /* 增加行高以便更好地显示多行文本 */
    }
  }
}
</style>
