<script lang="ts">
import { useJackpotData } from '../useJackpotData';

/**
 * Jackpot 领取弹窗模态框配置
 */
export const jackpotReceive = useAntdModal(
  defineAsyncComponent(() => import('./JackpotReceive.vue')),
  {
    wrapClassName: 'ant-cover__Modal-drawer __screen-drawer',
    centered: true,
    footer: null,
    closable: true,
    maskClosable: false,
  },
);
</script>

<script setup lang="ts">
// ============= 数据获取 =============

const { receiveReadyData, receiveReadyDataList, receiveMut } = useJackpotData();
const currencyStore = useCurrencyStore();
const { quote } = storeToRefs(currencyStore);

// ============= 响应式状态 =============

/** 选中的货币类型索引 (0: USDT, 1: 其他货币) */
const selectedCurrencyIndex = ref<number>(0);

/** 其他货币选项 */
const otherCurrency = computed((): string | undefined => {
  return receiveReadyDataList.value?.[1]?.currency;
});

// ============= 计算属性 =============

/**
 * 当前选中的货币
 */
const selectedCurrency = computed((): string => {
  return selectedCurrencyIndex.value === 0 ? 'USDT' : (otherCurrency.value ?? 'USDT');
});

/**
 * 奖励金额
 */
const rewardAmount = computed((): string => {
  return receiveReadyData.value?.amount ?? '0';
});

/**
 * 奖励货币
 */
const rewardCurrency = computed((): string => {
  return receiveReadyData.value?.currency ?? 'USDT';
});

// ============= 事件处理 =============

/**
 * 处理领取奖励
 */
function handleReceive(): void {
  receiveMut.mutate({
    currency: selectedCurrency.value,
  });
}

/**
 * 选择货币类型
 */
function selectCurrency(index: number): void {
  selectedCurrencyIndex.value = index;
}
</script>

<template>
  <div class="jackpot-receive mt-24px flex flex-col gap-4">
    <div class="jackpot-receive-top flex-center flex-col">
      <p class="text-sys-text-body">
        {{ $t('vaeCoCogNqnofLyzAoWk') }}
      </p>
      <div class="flex-center gap-2.5">
        <TCurrencyIcon class="ml-1" :symbol="rewardCurrency" size="28px" />
        <TAmount :value="rewardAmount" :decimals="2" symbol="$" format="fixed-dec" iconPos="left" class="text-7" />
      </div>
    </div>
    <div class="divider-h-gradual" />
    <div class="jackpot-receive-mian">
      <p>
        {{ $t('tsS1iB5ouxZzBeua4Bhhb') }}
      </p>
      <ARadioGroup v-model:value="selectedCurrencyIndex" class="jackpot-receive-main-content">
        <div
          class="jackpot-receive-main-content_item cursor-pointer"
          :class="{ active: selectedCurrencyIndex === 0 }"
          @click="selectCurrency(0)"
        >
          <ARadio :value="0" class="flex-start">
            <span>{{ $t('xFfUiUc1WwVFm_07PukUn') }} {{ receiveReadyData?.currency_balance_list?.[0].currency }}</span>
            <ATooltip
              class="van-cover__popver" placement="top"
              :title="$t('kkf3CQeduHpOHaJp0r', { quote: `${quote?.symbol}1`, rate: receiveReadyData?.currency_balance_list?.[0].exchange_rate })"
            >
              <i class="i-ri:question-line mb-1 ml-1 inline-block h-4 w-4 text-4 text-sys-text-body" />
            </ATooltip>
          </ARadio>
          <div class="divider-h-gradual" />
          <div class="flex-center gap-2.5 text-5.5">
            <TAmount :value="receiveReadyDataList?.[0]?.balance" :symbol="receiveReadyDataList?.[0]?.currency" :decimals="6" format="max-dec" colored="inherit" iconPos="left" :weakPad="true" />
            <span class="text-sys-text-body">{{ receiveReadyDataList?.[0]?.currency }}</span>
          </div>
        </div>
        <div class="jackpot-receive-main-content_item relative cursor-pointer" :class="{ active: selectedCurrencyIndex === 1 }" @click="selectCurrency(1)">
          <ARadio :value="1" class="flex-start">
            <span>{{ $t('au4VLlwpvDvs_09DXwOuZ', { num: !receiveReadyDataList?.length ? receiveReadyDataList?.length : receiveReadyDataList?.length - 1 }) }}</span>
          </ARadio>
          <div class="divider-h-gradual" />
          <div class="relative" :class="{ 'flex-between': selectedCurrencyIndex === 0 }">
            <AAvatarGroup v-if="selectedCurrencyIndex === 0">
              <TCurrencyIcon symbol="USDT" size="28px" />
              <TCurrencyIcon symbol="BNB" size="28px" />
            </AAvatarGroup>

            <div v-if="selectedCurrencyIndex === 0" class="absolute right-4 cursor-pointer text-sm text-yellow500">
              {{ false ? $t('g4Tk1PEua2J_9MzkVlWxz') : $t('wiPpGd0e7VTogE3ifHb') }}
              <i class="ml-2" :class="false ? 'i-ep:arrow-up' : 'i-ep:arrow-down' " />
            </div>
            <div v-show="selectedCurrencyIndex === 1" class="all-currency">
              <ARadioGroup v-model:value="otherCurrency" class="w-full">
                <div class="grid grid-cols-2 gap-2">
                  <template v-for="(item, key) in receiveReadyDataList" :key="key">
                    <ARadio v-if="!!key" class="flex-start text-4 leading-0" :value="item.currency">
                      <TCurrencyIcon :symbol="item.currency" size="20px " class="pb-1" />
                      <TAmount :value="item.balance" :decimals="6" format="pad-dec" iconPos="left" class="ml-2.5 text-4" :weakPad="true" />
                      <ATooltip
                        class="van-cover__popver" placement="top"
                        :title="$t('kkf3CQeduHpOHaJp0r', { quote: `${quote?.symbol}1`, rate: item.exchange_rate })"
                      >
                        <i class="i-ri:question-line mb-1 ml-1 inline-block h-4 w-4 text-4 text-sys-text-body" />
                      </ATooltip>
                    </ARadio>
                  </template>
                </div>
              </ARadioGroup>
            </div>
          </div>
        </div>
      </ARadioGroup>
    </div>

    <AButton class="ant-cover__Button-3d-primary" :class="{ 'ant-cover__Button-3d-gray': !!receiveReadyData?.allow_claim }" type="primary" block @click="!!receiveReadyData?.allow_claim && handleReceive()">
      领取奖励
    </AButton>
  </div>
</template>

<style scoped lang="scss">
.jackpot-receive {
  --uno: 'flex flex-col gap-4';

  .jackpot-receive-mian,
  .jackpot-receive-main-content {
    --uno: 'flex flex-col gap-4';
    .jackpot-receive-main-content_item {
      --uno: 'rounded-lg bg-sys-layer-a p-2.5 text-sys-text-head font-normal leading-normal text-base';
      &.active {
        border: 1.5px solid var(--ref-color-yellow-500, #eab308);
        background:
          radial-gradient(84.02% 123.6% at 16.73% -9.06%, rgba(234, 179, 8, 0.2) 0%, rgba(19, 20, 22, 0.2) 68.89%),
          var(--sys-layer-d, #26272e);
      }
    }
  }
}
</style>
