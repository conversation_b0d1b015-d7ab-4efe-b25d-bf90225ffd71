import { createSharedComposable, useIntervalFn } from '@peng_kai/kit/libs/vueuse'
import type {
  JackpotDataReturn,
  RankingStatistics,
  JackpotRule,
  PaginatedList,
  RankingItem,
  TopThreeRankings,
  JackpotReadyResponse,
  CurrencyInfo,
  RankingIcons
} from './types'
import {
  splitArrayByIndexParity,
  calculatePerSecondGrowth,
  updateLiveAmount,
  getCurrentTimestamp,
  safeSlice,
  REFRESH_INTERVAL,
  LIVE_UPDATE_INTERVAL,
  devLog
} from './utils'
import No1 from '~/pages/bonus/imgs/jackpot-no1.svg'
import No2 from '~/pages/bonus/imgs/jackpot-no2.svg'
import No3 from '~/pages/bonus/imgs/jackpot-no3.svg'

// ============= 常量定义 =============

/** 排行榜图标数组 */
export const RANKING_ICONS: RankingIcons = [No1, No2, No3] as const

// ============= 响应式断点 =============
const bp = useAppBreakpoints()

// ============= 主要 Composable =============

/**
 * Jackpot 数据管理 Composable
 * 提供奖池数据的获取、实时更新和状态管理功能
 */
export const useJackpotData = createSharedComposable((): JackpotDataReturn => {
  const currencyStore = useCurrencyStore()
  const { quote } = storeToRefs(currencyStore)

  // ============= 实时增长状态 =============
  const liveJackpotAmount = ref<string>('0')
  const historyJackpotAmount = ref<string>('0')
  const perSecondGrowth = ref<number>(0)
  const lastUpdateTime = ref<number>(getCurrentTimestamp())

  // ============= API 查询 =============

  /** 奖池配置查询 */
  const rulesConfigQry = useQuery({
    queryKey: [apis.apiActivityAviatorJackpotConfig.id],
    queryFn: () => apis.apiActivityAviatorJackpotConfig(undefined),
    refetchInterval: REFRESH_INTERVAL,
  })

  /** 排行榜数据查询 */
  const subjectQry = useQuery({
    queryKey: [apis.apiActivityAviatorJackpotRankings.id],
    queryFn: () => apis.apiActivityAviatorJackpotRankings(undefined),
  })

  // ============= 计算属性 =============

  /** 实时更新的奖池数据 */
  const subjectLive = computed((): RankingStatistics | undefined => {
    const originalData = subjectQry.data.value?.live
    if (!originalData) return undefined

    return {
      ...originalData,
      jackpot_amount: liveJackpotAmount.value,
    }
  })

  /** 历史奖池数据 */
  const subjectHistory = computed((): RankingStatistics | undefined => {
    return subjectQry.data.value?.history
  })

  /** 是否可以领取奖励 */
  const isReceive = computed((): boolean => {
    return (subjectLive.value?.my_rank?.can_claim ?? 0) === 1
  })

  /** 领取准备查询 */
  const receiveReadyQry = useQuery({
    enabled: isReceive,
    queryKey: [apis.apiActivityAviatorJackpotReady.id],
    queryFn: () => apis.apiActivityAviatorJackpotReady({ coin: quote.value?.currency }),
  })

  /** 查询参数 */
  const queryParams = ref<number>(1)

  /** 领取奖励变更 */
  const receiveMut = useMutation({
    mutationKey: [apis.apiActivityAviatorJackpotClaim.id],
    mutationFn: apis.apiActivityAviatorJackpotClaim,
  })

  // ============= 数据处理计算属性 =============

  /** 奖池配置规则数据 */
  const rulesConfigData = computed((): JackpotRule[] => {
    return rulesConfigQry.data.value?.rules ?? []
  })

  /** 实时排行榜列表（分页处理） */
  const subjectLiveList = computed((): PaginatedList<RankingItem> => {
    const list = subjectQry.data.value?.live?.list ?? []
    return bp.ltTablet ? [list] : splitArrayByIndexParity(list)
  })

  /** 历史排行榜列表（分页处理） */
  const subjectHistoryList = computed((): PaginatedList<RankingItem> => {
    const list = subjectQry.data.value?.history?.list ?? []
    return bp.ltTablet ? [list] : splitArrayByIndexParity(list)
  })

  /** 实时排行榜前三名 */
  const subjectLiveTopThree = computed((): TopThreeRankings => {
    if (bp.ltTablet) {
      return safeSlice(subjectLiveList.value[0], 3)
    }

    const [evenList, oddList] = subjectLiveList.value
    return [
      evenList?.[0],
      oddList?.[0],
      evenList?.[1],
    ]
  })

  /** 领取准备数据 */
  const receiveReadyData = computed((): JackpotReadyResponse | undefined => {
    return receiveReadyQry.data.value
  })

  /** 领取准备货币列表 */
  const receiveReadyDataList = computed((): CurrencyInfo[] => {
    return receiveReadyQry.data.value?.currency_balance_list ?? []
  })

  // ============= 实时更新逻辑 =============

  /**
   * 计算每秒增长金额
   * 监听配置数据变化，更新每秒增长率
   */
  watchEffect(() => {
    const incomeHourly = rulesConfigQry.data.value?.income_hourly
    if (incomeHourly) {
      perSecondGrowth.value = calculatePerSecondGrowth(incomeHourly)
      devLog('Updated per second growth', { incomeHourly, perSecondGrowth: perSecondGrowth.value })
    }
  })

  /**
   * 更新基础金额和时间戳
   * 监听查询数据变化，同步本地状态
   */
  watchEffect(() => {
    const liveData = subjectQry.data.value?.live
    const historyData = subjectQry.data.value?.history

    if (liveData?.jackpot_amount) {
      liveJackpotAmount.value = liveData.jackpot_amount
      lastUpdateTime.value = getCurrentTimestamp()
      devLog('Updated live jackpot amount', { amount: liveData.jackpot_amount })
    }

    if (historyData?.jackpot_amount) {
      historyJackpotAmount.value = historyData.jackpot_amount
    }
  })

  /**
   * 实时更新定时器
   * 每秒更新奖池金额
   */
  const { pause: pauseTimer, resume: resumeTimer } = useIntervalFn(() => {
    if (perSecondGrowth.value > 0) {
      liveJackpotAmount.value = updateLiveAmount(liveJackpotAmount.value, perSecondGrowth.value)
    }
  }, LIVE_UPDATE_INTERVAL)

  // ============= 生命周期管理 =============

  /**
   * 组件卸载时清理定时器
   */
  onUnmounted(() => {
    pauseTimer()
  })

  // ============= 返回接口 =============

  return {
    // 查询相关
    rulesConfigQry,
    subjectQry,
    receiveReadyQry,
    receiveMut,

    // 计算属性
    rulesConfigData,
    subjectLive,
    subjectHistory,
    subjectLiveList,
    subjectHistoryList,
    subjectLiveTopThree,
    receiveReadyData,
    receiveReadyDataList,

    // 响应式状态
    queryParams,
    liveJackpotAmount,
    historyJackpotAmount,
    perSecondGrowth,

    // 控制方法
    pauseTimer,
    resumeTimer,
  } as const
})

// ============= 导出的工具和常量 =============

/**
 * 排行榜图标（响应式）
 * @deprecated 建议使用 RANKING_ICONS 常量
 */
export const icons = reactive(RANKING_ICONS)

/**
 * 列数计算（响应式）
 * 根据屏幕尺寸决定显示列数
 */
export const cols = computed((): number => bp.tablet ? 2 : 1)

// ============= 工具函数 =============

// 导出工具函数以保持向后兼容
export { splitArrayByIndexParity } from './utils'
