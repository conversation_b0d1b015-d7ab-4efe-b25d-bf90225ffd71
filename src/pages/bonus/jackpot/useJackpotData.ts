import { createSharedComposable, useIntervalFn } from '@peng_kai/kit/libs/vueuse';
import No1 from '~/pages/bonus/imgs/jackpot-no1.svg';
import No2 from '~/pages/bonus/imgs/jackpot-no2.svg';
import No3 from '~/pages/bonus/imgs/jackpot-no3.svg';

const bp = useAppBreakpoints();
export const useJackpotData = createSharedComposable(() => {
  const currencyStore = useCurrencyStore();
  const { quote } = storeToRefs(currencyStore);

  // 实时增长相关状态
  const liveJackpotAmount = ref<string>('0');
  const historyJackpotAmount = ref<string>('0');
  const perSecondGrowth = ref<number>(0);
  const lastUpdateTime = ref<number>(Date.now());

  const rulesConfigQry = useQuery({
    queryKey: [apis.apiActivityAviatorJackpotConfig.id],
    queryFn: () => apis.apiActivityAviatorJackpotConfig(undefined),
    refetchInterval: 10000, // 每10秒刷新一次
  });
  const subjectQry = useQuery({
    queryKey: [apis.apiActivityAviatorJackpotRankings.id],
    queryFn: () => apis.apiActivityAviatorJackpotRankings(undefined),
  });

  // 返回实时更新的jackpot数据
  const subjectLive = computed(() => {
    const originalData = subjectQry.data.value?.live;
    if (!originalData)
      return undefined;

    return {
      ...originalData,
      jackpot_amount: liveJackpotAmount.value,
    };
  });

  // 是否可领取
  const isReceive = computed(() => (subjectLive.value?.my_rank?.can_claim ?? 0) === 1);

  const receiveReadyQry = useQuery({
    enabled: isReceive.value,
    queryKey: [apis.apiActivityAviatorJackpotReady.id],
    queryFn: () => apis.apiActivityAviatorJackpotReady({ coin: quote.value?.currency }),
  });

  // 响应式参数
  const queryParams = ref(1);

  const receiveMut = useMutation({
    mutationKey: [apis.apiActivityAviatorJackpotClaim.id],
    mutationFn: apis.apiActivityAviatorJackpotClaim,
  });

  // 计算每秒增长金额并更新实时jackpot金额
  watchEffect(() => {
    const incomeHourly = rulesConfigQry.data.value?.income_hourly;
    if (incomeHourly) {
      // 将每小时增长转换为每秒增长
      perSecondGrowth.value = Number.parseFloat(incomeHourly) / 3600;
    }
  });

  // 当获取到新数据时，更新基础金额和时间戳
  watchEffect(() => {
    const liveData = subjectQry.data.value?.live;
    const historyData = subjectQry.data.value?.history;

    if (liveData?.jackpot_amount) {
      liveJackpotAmount.value = liveData.jackpot_amount;
      lastUpdateTime.value = Date.now();
    }

    if (historyData?.jackpot_amount) {
      historyJackpotAmount.value = historyData.jackpot_amount;
    }
  });

  // 每秒更新jackpot金额
  const { pause: pauseTimer, resume: resumeTimer } = useIntervalFn(() => {
    if (perSecondGrowth.value > 0) {
      // 更新live jackpot金额
      const currentLiveAmount = Number.parseFloat(liveJackpotAmount.value);
      const newLiveAmount = currentLiveAmount + perSecondGrowth.value;
      liveJackpotAmount.value = newLiveAmount.toFixed(2);
    }
  }, 1000);

  const rulesConfigData = computed(() => rulesConfigQry.data.value?.rules ?? []);

  const subjectHistory = computed(() => {
    const originalData = subjectQry.data.value?.history;
    if (!originalData)
      return undefined;

    return originalData;
  });
  const subjectLiveList = computed(() => {
    if (bp.ltTablet)
      return [subjectQry.data.value?.live?.list];
    return splitArrayByIndexParity(subjectQry.data.value?.live?.list ?? []);
  });
  const receiveReadyData = computed(() => receiveReadyQry.data.value ?? undefined);
  const receiveReadyDataList = computed(() => receiveReadyQry.data.value?.currency_balance_list ?? []);

  const subjectHistoryList = computed(() => {
    if (bp.ltTablet)
      return [subjectQry.data.value?.history?.list];
    return splitArrayByIndexParity(subjectQry.data.value?.history?.list ?? []);
  });

  const subjectLiveTopThree = computed(() => {
    if (bp.ltTablet) {
      return subjectLiveList.value[0]?.slice?.(0, 3);
    }
    else {
      return [
        subjectLiveList.value[0][0],
        subjectLiveList.value[1][0],
        subjectLiveList.value[0][1],
      ];
    }
  });

  // 组件卸载时清理定时器
  onUnmounted(() => {
    pauseTimer();
  });

  return {
    rulesConfigQry,
    subjectQry,
    rulesConfigData,
    subjectLive,
    subjectHistory,
    queryParams,
    subjectLiveList,
    subjectHistoryList,
    subjectLiveTopThree,
    receiveReadyQry,
    receiveMut,
    receiveReadyData,
    receiveReadyDataList,
    // 实时增长相关
    liveJackpotAmount,
    historyJackpotAmount,
    perSecondGrowth,
    pauseTimer,
    resumeTimer,
  };
});

export const icons = reactive([
  No1,
  No2,
  No3,
]);

export const cols = computed(() => bp.tablet ? 2 : 1);

/**
 * 将数组按照单数索引和双数索引分成二维数组
 * @param arr 原数组
 * @returns [[偶数索引数组], [奇数索引数组]]
 *
 * @example
 * splitArrayByIndexParity([1, 2, 3, 4, 5, 6, 7, 8, 9])
 * // 返回: [[1, 3, 5, 7, 9], [2, 4, 6, 8]]
 */
export function splitArrayByIndexParity<T>(arr: T[]): T[][] {
  const evenIndexItems: T[] = [];
  const oddIndexItems: T[] = [];

  arr.forEach((item, index) => {
    if (index % 2 === 0) {
      evenIndexItems.push(item);
    }
    else {
      oddIndexItems.push(item);
    }
  });

  return [evenIndexItems, oddIndexItems];
}
