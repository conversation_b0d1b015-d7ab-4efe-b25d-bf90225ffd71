/**
 * Jackpot 功能相关的工具函数
 */

import type { RankingItem, RankingIcons } from './types'

// ============= 常量 =============

/** 每小时转每秒的转换系数 */
export const HOUR_TO_SECOND_RATIO = 3600

/** 数据刷新间隔（毫秒） */
export const REFRESH_INTERVAL = 10000

/** 实时更新间隔（毫秒） */
export const LIVE_UPDATE_INTERVAL = 1000

/** 前三名排行榜阈值 */
export const TOP_THREE_THRESHOLD = 3

// ============= 数组处理工具 =============

/**
 * 将数组按照索引奇偶性分成两个数组
 * 
 * @template T 数组元素类型
 * @param arr 要分割的原数组
 * @returns 包含两个数组的元组：[偶数索引数组, 奇数索引数组]
 * 
 * @example
 * ```typescript
 * splitArrayByIndexParity([1, 2, 3, 4, 5, 6, 7, 8, 9])
 * // 返回: [[1, 3, 5, 7, 9], [2, 4, 6, 8]]
 * ```
 */
export function splitArrayByIndexParity<T>(arr: T[]): [T[], T[]] {
  const evenIndexItems: T[] = []
  const oddIndexItems: T[] = []

  arr.forEach((item, index) => {
    if (index % 2 === 0) {
      evenIndexItems.push(item)
    } else {
      oddIndexItems.push(item)
    }
  })

  return [evenIndexItems, oddIndexItems]
}

/**
 * 安全获取数组的前N个元素
 * 
 * @template T 数组元素类型
 * @param arr 源数组
 * @param count 要获取的元素数量
 * @returns 前N个元素的数组
 */
export function safeSlice<T>(arr: T[] | undefined, count: number): T[] {
  return arr?.slice(0, count) ?? []
}

// ============= 排行榜相关工具 =============

/**
 * 获取排行榜图标
 * 
 * @param rank 排名
 * @param icons 图标数组
 * @returns 图标URL或null
 */
export function getRankIcon(rank: number, icons: RankingIcons): string | null {
  return rank <= TOP_THREE_THRESHOLD ? icons[rank - 1] : null
}

/**
 * 格式化排名显示
 * 
 * @param rankNo 排名号码
 * @returns 格式化后的排名字符串
 */
export function formatRankDisplay(rankNo: number | undefined): string {
  return rankNo ? String(rankNo) : 'No Rank'
}

/**
 * 检查是否为前三名
 * 
 * @param rank 排名
 * @returns 是否为前三名
 */
export function isTopThree(rank: number): boolean {
  return rank <= TOP_THREE_THRESHOLD
}

// ============= 数值处理工具 =============

/**
 * 安全的数值解析
 * 
 * @param value 要解析的值
 * @param defaultValue 默认值
 * @returns 解析后的数值
 */
export function safeParseFloat(value: string | number | undefined, defaultValue = 0): number {
  if (typeof value === 'number') return value
  if (typeof value === 'string') {
    const parsed = Number.parseFloat(value)
    return Number.isNaN(parsed) ? defaultValue : parsed
  }
  return defaultValue
}

/**
 * 格式化奖励比例为百分比
 * 
 * @param rate 奖励比例字符串
 * @returns 百分比数值
 */
export function formatRewardRateToPercentage(rate: string | undefined): number {
  return safeParseFloat(rate) * 100
}

/**
 * 计算每秒增长金额
 * 
 * @param incomeHourly 每小时收入
 * @returns 每秒增长金额
 */
export function calculatePerSecondGrowth(incomeHourly: string | undefined): number {
  return safeParseFloat(incomeHourly) / HOUR_TO_SECOND_RATIO
}

/**
 * 更新实时金额
 * 
 * @param currentAmount 当前金额
 * @param growthPerSecond 每秒增长金额
 * @param decimals 小数位数
 * @returns 更新后的金额字符串
 */
export function updateLiveAmount(
  currentAmount: string,
  growthPerSecond: number,
  decimals = 2
): string {
  const current = safeParseFloat(currentAmount)
  const newAmount = current + growthPerSecond
  return newAmount.toFixed(decimals)
}

// ============= 货币相关工具 =============

/**
 * 格式化货币金额
 * 
 * @param amount 金额
 * @param decimals 小数位数
 * @returns 格式化后的金额字符串
 */
export function formatCurrencyAmount(amount: string | number | undefined, decimals = 2): string {
  const numAmount = safeParseFloat(amount)
  return numAmount.toFixed(decimals)
}

/**
 * 检查金额是否有效
 * 
 * @param amount 金额
 * @returns 是否有效
 */
export function isValidAmount(amount: string | number | undefined): boolean {
  const numAmount = safeParseFloat(amount)
  return numAmount > 0
}

// ============= 时间相关工具 =============

/**
 * 获取当前时间戳
 * 
 * @returns 当前时间戳
 */
export function getCurrentTimestamp(): number {
  return Date.now()
}

/**
 * 计算时间差（秒）
 * 
 * @param startTime 开始时间戳
 * @param endTime 结束时间戳（默认为当前时间）
 * @returns 时间差（秒）
 */
export function getTimeDifferenceInSeconds(
  startTime: number,
  endTime = getCurrentTimestamp()
): number {
  return Math.floor((endTime - startTime) / 1000)
}

// ============= 验证工具 =============

/**
 * 验证排行榜项数据
 * 
 * @param item 排行榜项
 * @returns 是否有效
 */
export function isValidRankingItem(item: RankingItem | undefined): item is RankingItem {
  return !!(item?.rank_no && item?.user?.nickname && item?.bet_amount)
}

/**
 * 验证货币代码
 * 
 * @param currency 货币代码
 * @returns 是否有效
 */
export function isValidCurrency(currency: string | undefined): currency is string {
  return typeof currency === 'string' && currency.length > 0
}

// ============= 调试工具 =============

/**
 * 开发环境日志输出
 *
 * @param message 消息
 * @param data 数据
 */
export function devLog(message: string, data?: any): void {
  if (typeof window !== 'undefined' && (window as any).__DEV__) {
    console.log(`[Jackpot] ${message}`, data)
  }
}

/**
 * 开发环境错误输出
 *
 * @param message 错误消息
 * @param error 错误对象
 */
export function devError(message: string, error?: any): void {
  if (typeof window !== 'undefined' && (window as any).__DEV__) {
    console.error(`[Jackpot Error] ${message}`, error)
  }
}
