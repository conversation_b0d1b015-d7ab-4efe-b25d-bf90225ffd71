/**
 * Jackpot 功能相关的工具函数
 * 简化版本，只保留必要的工具函数
 */

import type { RankingIcons } from './types'

// ============= 常量 =============

/** 前三名排行榜阈值 */
export const TOP_THREE_THRESHOLD = 3

// ============= 排行榜相关工具 =============

/**
 * 获取排行榜图标
 */
export function getRankIcon(rank: number, icons: RankingIcons): string | null {
  return rank <= TOP_THREE_THRESHOLD ? icons[rank - 1] : null
}

/**
 * 格式化排名显示
 */
export function formatRankDisplay(rankNo: number | undefined): string {
  return rankNo ? String(rankNo) : 'No Rank'
}

/**
 * 格式化奖励比例为百分比
 */
export function formatRewardRateToPercentage(rate: string | undefined): number {
  const numRate = Number.parseFloat(rate || '0')
  return Number.isNaN(numRate) ? 0 : numRate * 100
}


