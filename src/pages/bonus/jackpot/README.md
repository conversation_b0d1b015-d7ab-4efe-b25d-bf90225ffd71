# Jackpot 功能模块

这是一个完全重构和优化的 Jackpot 功能模块，采用 TypeScript 严格类型检查，提供了清晰的代码结构和完善的类型定义。

## 📁 文件结构

```
src/pages/bonus/jackpot/
├── README.md                    # 文档说明
├── types.ts                     # TypeScript 类型定义
├── utils.ts                     # 工具函数
├── useJackpotData.ts           # 主要 Composable
├── index.vue                   # 主页面组件
├── style.scss                  # 样式文件
└── components/                 # 子组件
    ├── JackpotHeader.vue       # 头部组件
    ├── JackpotHistory.vue      # 历史记录组件
    ├── JackpotReceive.vue      # 领取奖励组件
    └── JackpotRules.vue        # 规则说明组件
```

## 🔧 核心功能

### 1. 实时奖池更新
- 自动获取奖池配置和排行榜数据
- 基于每小时增长率计算每秒增长
- 实时更新奖池金额显示

### 2. 排行榜管理
- 支持实时和历史排行榜
- 响应式布局适配（移动端/桌面端）
- 前三名特殊图标显示

### 3. 奖励领取
- 检查领取资格
- 支持多种货币选择
- 安全的领取流程

## 📋 类型定义

### 主要接口

```typescript
// 排行榜项
interface RankingItem {
  bet_amount: string
  bet_amount_need?: string
  can_claim?: number
  rank_no: number
  reward_rate: string
  user?: UserLite
}

// 排行榜统计
interface RankingStatistics {
  jackpot_amount: string
  list: RankingItem[]
  my_rank?: RankingItem
}

// Composable 返回类型
interface JackpotDataReturn {
  // 查询相关
  rulesConfigQry: any
  subjectQry: any
  receiveReadyQry: any
  receiveMut: any
  
  // 计算属性
  rulesConfigData: ComputedRef<JackpotRule[]>
  subjectLive: ComputedRef<RankingStatistics | undefined>
  subjectHistory: ComputedRef<RankingStatistics | undefined>
  // ... 更多属性
}
```

## 🛠️ 工具函数

### 数组处理
- `splitArrayByIndexParity<T>(arr: T[]): [T[], T[]]` - 按索引奇偶性分割数组
- `safeSlice<T>(arr: T[], count: number): T[]` - 安全获取数组前N个元素

### 数值处理
- `safeParseFloat(value, defaultValue)` - 安全的数值解析
- `formatRewardRateToPercentage(rate)` - 格式化奖励比例
- `calculatePerSecondGrowth(incomeHourly)` - 计算每秒增长
- `updateLiveAmount(currentAmount, growthPerSecond)` - 更新实时金额

### 排行榜工具
- `getRankIcon(rank, icons)` - 获取排行榜图标
- `formatRankDisplay(rankNo)` - 格式化排名显示
- `isTopThree(rank)` - 检查是否为前三名

### 验证工具
- `isValidRankingItem(item)` - 验证排行榜项
- `isValidCurrency(currency)` - 验证货币代码
- `isValidAmount(amount)` - 检查金额有效性

## 🎯 使用方式

### 在组件中使用

```vue
<script setup lang="ts">
import { useJackpotData } from '../useJackpotData'
import { getRankIcon, formatRankDisplay } from '../utils'
import { RANKING_ICONS } from '../useJackpotData'

const { 
  subjectLive, 
  subjectHistory, 
  rulesConfigData 
} = useJackpotData()

// 获取排行榜图标
const getIcon = (rank: number) => getRankIcon(rank, RANKING_ICONS)

// 格式化排名
const formatRank = (rankNo: number | undefined) => formatRankDisplay(rankNo)
</script>
```

### 类型安全

```typescript
// 所有数据都有完整的类型定义
const rankingItem: RankingItem = {
  bet_amount: "100.00",
  rank_no: 1,
  reward_rate: "0.05",
  user: {
    nickname: "Player1"
  }
}

// 类型检查确保数据安全
if (isValidRankingItem(rankingItem)) {
  // TypeScript 知道这里 rankingItem 是有效的
  console.log(rankingItem.user.nickname)
}
```

## 🔄 状态管理

### 响应式状态
- `liveJackpotAmount` - 实时奖池金额
- `historyJackpotAmount` - 历史奖池金额
- `perSecondGrowth` - 每秒增长金额
- `queryParams` - 查询参数

### 计算属性
- `subjectLive` - 实时排行榜数据（包含实时更新的金额）
- `subjectHistory` - 历史排行榜数据
- `subjectLiveList` - 分页处理的实时排行榜
- `subjectLiveTopThree` - 前三名数据

## 🎨 组件特性

### JackpotHeader
- 显示实时奖池金额
- 领取按钮状态管理
- 响应式设计

### JackpotHistory
- 历史排行榜展示
- 双列表同步滚动
- 我的排名信息

### JackpotReceive
- 奖励领取界面
- 多货币选择
- 安全验证

### JackpotRules
- 规则说明展示
- 奖励比例表格
- 响应式布局

## 🚀 性能优化

1. **共享状态管理** - 使用 `createSharedComposable` 避免重复请求
2. **计算属性缓存** - 合理使用 `computed` 减少不必要的计算
3. **类型安全** - 完整的 TypeScript 类型定义避免运行时错误
4. **工具函数复用** - 提取通用逻辑到工具函数
5. **开发调试** - 开发环境下的调试日志输出

## 🔧 开发建议

1. **类型优先** - 始终为新功能添加类型定义
2. **工具函数** - 将通用逻辑提取到 `utils.ts`
3. **错误处理** - 使用 `safe*` 系列函数进行安全处理
4. **性能监控** - 利用开发环境的调试工具
5. **代码复用** - 优先使用现有的工具函数和类型

## 📝 更新日志

### v2.0.0 (重构版本)
- ✅ 完整的 TypeScript 类型定义
- ✅ 模块化的工具函数
- ✅ 优化的组件结构
- ✅ 改进的状态管理
- ✅ 增强的错误处理
- ✅ 完善的文档说明

### 迁移指南
- 原有的 `icons` 数组改为 `RANKING_ICONS` 常量
- 工具函数移至 `utils.ts` 文件
- 所有组件都添加了完整的类型定义
- 改进了计算属性的命名和逻辑
