/**
 * Jackpot 功能相关的 TypeScript 类型定义
 */

// 导入Vue类型
import type { ComputedRef, Ref } from 'vue'

// ============= 基础类型 =============

/** 货币信息 */
export interface CurrencyInfo {
  currency: string
  balance?: string
  exchange_rate?: string
  is_default?: boolean
  logo: string
  name: string
}

/** 用户简要信息 */
export interface UserLite {
  nickname: string
  uid?: string
  level?: number
  avatar?: string
}

// ============= API 相关类型 =============

/** Jackpot 配置规则项 */
export interface JackpotRule {
  id: number
  rank: string
  reward_rate: string
}

/** Jackpot 配置响应 */
export interface JackpotConfigResponse {
  income_hourly: string
  rules: JackpotRule[]
}

/** 排行榜项详情 */
export interface RankingItem {
  bet_amount: string
  bet_amount_need?: string
  can_claim?: number
  rank_no: number
  reward_rate: string
  user?: UserLite
}

/** 排行榜统计数据 */
export interface RankingStatistics {
  jackpot_amount: string
  list: RankingItem[]
  my_rank?: RankingItem
}

/** 排行榜响应数据 */
export interface JackpotRankingsResponse {
  history: RankingStatistics
  live: RankingStatistics
}

/** 领取准备响应 */
export interface JackpotReadyResponse {
  allow_claim: number
  amount: string
  currency: string
  currency_balance_list: CurrencyInfo[]
  tips: string
}

/** 领取请求参数 */
export interface JackpotClaimRequest {
  currency: string
}

/** 领取响应 */
export interface JackpotClaimResponse {
  allow_claim: number
  currency_balance_list: CurrencyInfo[]
  tips: string
}

// ============= 组件相关类型 =============

/** 实时增长状态 */
export interface LiveGrowthState {
  /** 当前实时奖池金额 */
  liveJackpotAmount: string
  /** 历史奖池金额 */
  historyJackpotAmount: string
  /** 每秒增长金额 */
  perSecondGrowth: number
  /** 最后更新时间戳 */
  lastUpdateTime: number
}

/** 查询参数 */
export interface QueryParams {
  /** 查询类型 1-实时 2-历史 */
  type: number
}

/** 分页列表数据 */
export type PaginatedList<T> = T[][]

/** 排行榜前三名数据 */
export type TopThreeRankings = (RankingItem | undefined)[]

// ============= Composable 返回类型 =============

/** useJackpotData 返回的数据结构 */
export interface JackpotDataReturn {
  // 查询相关
  rulesConfigQry: any // TODO: 添加具体的 Query 类型
  subjectQry: any
  receiveReadyQry: any
  receiveMut: any
  
  // 计算属性
  rulesConfigData: ComputedRef<JackpotRule[]>
  subjectLive: ComputedRef<RankingStatistics | undefined>
  subjectHistory: ComputedRef<RankingStatistics | undefined>
  subjectLiveList: ComputedRef<PaginatedList<RankingItem>>
  subjectHistoryList: ComputedRef<PaginatedList<RankingItem>>
  subjectLiveTopThree: ComputedRef<TopThreeRankings>
  receiveReadyData: ComputedRef<JackpotReadyResponse | undefined>
  receiveReadyDataList: ComputedRef<CurrencyInfo[]>
  
  // 响应式状态
  queryParams: Ref<number>
  liveJackpotAmount: Ref<string>
  historyJackpotAmount: Ref<string>
  perSecondGrowth: Ref<number>
  
  // 控制方法
  pauseTimer: () => void
  resumeTimer: () => void
}

// ============= 组件 Props 类型 =============

/** JackpotHeader 组件的 emits */
export interface JackpotHeaderEmits {
  onReceive: []
}

/** JackpotReceive 组件状态 */
export interface JackpotReceiveState {
  /** 选中的货币类型索引 */
  selectedCurrencyIndex: number
  /** 其他货币选项 */
  otherCurrency?: string
}

// ============= 工具函数类型 =============

/** 数组分割函数的返回类型 */
export type SplitArrayResult<T> = [T[], T[]]

// ============= 常量类型 =============

/** 图标资源类型 */
export type IconResource = string

/** 排行榜图标数组 */
export type RankingIcons = readonly [IconResource, IconResource, IconResource]

// ============= 扩展现有类型 =============

/** 扩展的排行榜统计数据（包含实时更新） */
export interface EnhancedRankingStatistics extends Omit<RankingStatistics, 'jackpot_amount'> {
  jackpot_amount: string // 实时更新的金额
}

// ============= 错误处理类型 =============

/** API 错误响应 */
export interface ApiErrorResponse {
  code: number
  msg: string
  request_id: string
}

/** 业务错误类型 */
export type JackpotError = 
  | 'NETWORK_ERROR'
  | 'CLAIM_NOT_AVAILABLE'
  | 'INSUFFICIENT_BALANCE'
  | 'INVALID_CURRENCY'
  | 'UNKNOWN_ERROR'

// ============= 事件类型 =============

/** 组件事件类型 */
export interface JackpotEvents {
  /** 领取奖励事件 */
  onReceive: () => void
  /** 查看历史事件 */
  onViewHistory: () => void
  /** 查看规则事件 */
  onViewRules: () => void
}
