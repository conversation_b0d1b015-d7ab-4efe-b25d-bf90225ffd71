import { agreementAccountFromTgStorage } from '~/components-business/auth/CreateAccountFromTg.vue';
import { subcriptionNotice } from '~/components/provider/helpers/forPwa';
import { useAuthed } from '~/composables/useAuthed';
import cs from '~/composables/useCS';
import { fcmServer } from '~/composables/useFCMService';
import { useAppSocket } from '~/modules/socket/useAppSocket';
import { getNotifyPromission, setNotifyPromission } from '~/utils';

export const useUserStore = defineStore('user', () => {
  const authed = useAuthed();
  const appSocket = useAppSocket();
  const apkToken = ref('');
  let apkTokenRetryCount = 0;

  const userInfoQry = useQuery({
    enabled: authed,
    queryKey: [apis.apiUserInfo.id],
    queryFn: () => apis.apiUserInfo(undefined),
    refetchInterval: 10 * 1000,
    refetchOnWindowFocus: true,
  });
  const userInfo = computed(() => userInfoQry.data.value);
  // 用户类型 1临时用户（无帐变）2意向用户（只有测试币帐变）3有效用户（有正式币或充值）4高净值客户（充值满10万）5超级大户（充值满100万）6（风险客户：洗钱嫌疑，薅羊毛，找漏洞）7试玩用户(不统计报表)
  const userType = computed(() => userInfo.value?.type);
  const verified = computed(() => {
    const _userInfo = userInfo.value;

    if (authed.value && _userInfo) {
      return {
        email: !!(_userInfo.email && _userInfo.email_verified),
        authenticator: !!_userInfo.enable_authenticator,
      };
    }
  });
  const noticeSubscriptionsRegisterMut = useMutation({
    mutationKey: [apis.apiNoticeSubscriptionsRegister.id],
    mutationFn: apis.apiNoticeSubscriptionsRegister,
  });

  /** apk注册和检查token操作 */
  const apkTokenRegisterMut = useMutation({
    mutationKey: [apis.apiNoticeFcmSubcription.id],
    mutationFn: apis.apiNoticeFcmSubcription.setDefaultConfig({ errorMessage: false }),
    onSuccess() {
      sessionStorage.setItem('APKPushIsRegister', '1');
    },
  });
  const checkApkNotifyTokenQry = useQuery({
    enabled: false,
    queryKey: [apis.apiNoticeFcmCheckToken.id],
    queryFn: () => apis.apiNoticeFcmCheckToken(undefined, {
      params: { token: apkToken.value },
      errorMessage: false,
      validateStatus(status) {
        if (status === 200) {
          apkTokenRegisterMut.mutateAsync({ requestBody: { token: apkToken.value, topic: window.source } });
          return true;
        }
        fcmServer.refreshFCMToken().then(t => apkToken.value = t || '');
        return false;
      },
    }),
  });

  appSocket.receive('USER_INFO_RESP', 'UserDetail', () => {
    userInfoQry.refetch();
  });

  appSocket.receive('USER_LEVEL_UPGRADE_RESP', 'UserDetail', () => {
    queryClient.invalidateQueries({ queryKey: [apis.apiLevelInfo.id], exact: false });
    userInfoQry.refetch();
  });

  async function logout() {
    await apis.apiAuthLogout({});
    cs.clear();
    agreementAccountFromTgStorage.remove();
  }

  watch(() => userInfo.value?.uid, (v) => {
    if (v) {
      useNotice(true, () => {
        const data = getNotifyPromission();
        noticeSubscriptionsRegisterMut.mutateAsync({ requestBody: {
          endpoint: data.endpoint,
          auth: data.keys.auth,
          p256dh: data.keys.p256dh,
        } });
      });
      // 验证APK权限
      if (envs.isInAndroidApp && fcmServer.getTokenState()) {
        apkToken.value = fcmServer.getFCMToken();
        checkApkNotifyTokenQry.refetch();
      }
    }
  }, { immediate: true });

  return {
    /** 用户信息查询器 */
    userInfoQry,
    /** 用户信息 */
    userInfo,
    /** 用户类型 */
    userType,
    /** 验证状态 */
    verified,
    /** 登出 */
    logout,
  };
});

async function useNotice(userAuthed: boolean, callback: () => void) {
  // 创建一个监听器 每3秒检查或申请一次通知权限，直到用户同意
  await createConditionListener(async () => {
    if (!window.swObj || !window.noticePubKey || !userAuthed)
      return false;
    // 如果是ios且是pwa环境，并且用户没有拒绝过权限，则弹出弹窗,用户拒绝或已经授权，则不弹出
    const info = getNotifyPromission();
    if (envs.isMobile && envs.isPwa && info?.endpoint === undefined) {
      globalPopups.noticeSubscription.open();
      return true;
    }
    // 不在pwa环境下，用户拒绝过权限则清空拒绝状态
    else if (envs.isIos && !envs.isPwa && info?.endpoint === false) {
      setNotifyPromission({});
    }
    let sub = await window.swObj.pushManager.getSubscription();
    if (sub) {
      setNotifyPromission(sub);
    }
    else {
      sub = await subcriptionNotice();
    };
    if (!sub.endpoint)
      return false;
    callback();
    return true;
  }, 3000);

  /**
   * 监听器
   * @param conditionFn 条件检查函数，返回 true 表示条件成立
   * @param interval 检查间隔时间（毫秒），默认 100ms
   * @param timeout 超时时间（毫秒），可选，不设置则不会超时
   * @returns 返回一个 Promise，当条件成立时 resolve，超时或出错时 reject
   */
  function createConditionListener<T = void>(
    conditionFn: () => boolean | Promise<boolean>,
    interval: number = 1000,
    timeout?: number,
  ): Promise<T> {
    return new Promise((resolve, reject) => {
    // 检查超时
      let timeoutId: ReturnType<typeof setTimeout> | null = null;
      if (timeout) {
        timeoutId = setTimeout(() => {
          cleanup();
          reject(new Error(`Condition check timed out after ${timeout}ms`));
        }, timeout);
      }

      // 检查间隔
      const intervalId = setInterval(async () => {
        try {
          const result = await conditionFn();
          if (result) {
            cleanup();
            resolve(undefined as T); // 使用类型断言，因为实际值可能由 conditionFn 提供
          }
        }
        catch (error) {
          cleanup();
          reject(error);
        }
      }, interval);

      // 清理函数
      const cleanup = () => {
        clearInterval(intervalId);
        if (timeoutId)
          clearTimeout(timeoutId);
      };

      // 初始检查
      (async () => {
        try {
          const initialResult = await conditionFn();
          if (initialResult) {
            cleanup();
            resolve(undefined as T);
          }
        }
        catch (error) {
          cleanup();
          reject(error);
        }
      })();
    });
  }
}
