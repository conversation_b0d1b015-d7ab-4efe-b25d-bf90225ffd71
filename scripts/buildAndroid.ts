import { exec, execSync } from 'node:child_process';
import path from 'node:path';
import process from 'node:process';
import fs from 'fs-extra';

// apk输出存放位置
const outputDir = 'D:/work/androidAPKs';
const projectDir = process.cwd();
const buildCommands = ['.\\gradlew clean', '.\\gradlew assembleRelease'];
const androidDir = path.join(projectDir, 'android');
const androidOutput = path.join(androidDir, 'app/build/outputs/apk/release/app-release.apk');
// 测试包
const buildProjectCommand = 'vite build --mode staging && npx cap sync';
// const buildProjectCommand = 'set NODE_ENV=production && vite build && npx cap sync';
const channels = ['W2ATEST01', 'W2ATEST'];

async function main() {
  for (let i = 0; i < channels.length; i++) {
    await editFileChannel(channels[i]);
    await buildProject();
    await buildFn(channels[i]);
  }
}

async function buildFn(channel: string) {
  console.log('生成apk');
  for (let i = 0; i < buildCommands.length; i++) {
    await execSync(buildCommands[i], { cwd: androidDir, encoding: 'utf-8' });
  }
  // 1. 确保目标目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`创建目录: ${outputDir}`);
  }

  const moveCommand = process.platform === 'win32'
    ? `move /Y "${androidOutput}" "${outputDir}/${channel}.apk"`
    : `mv -f "${androidOutput}" "${outputDir}/${channel}.apk"`;

  const child = exec(moveCommand);
  await new Promise((resolve, reject) => {
    child.on('exit', async (code) => {
      if (code === 0) {
        resolve(`文件移动成功`);
      }
      else {
        reject(new Error(`移动命令执行失败，退出码: ${code}`));
      }
    });

    child.on('error', (err) => {
      reject(err);
    });
  });
}

async function buildProject() {
  const child = exec(buildProjectCommand);
  console.log('项目构建中');
  await new Promise((resolve, reject) => {
    child.on('exit', async (code) => {
      if (code === 0) {
        resolve(`打包成功`);
      }
      else {
        reject(new Error(`打包失败: ${code}`));
      }
    });

    child.on('error', (err) => {
      reject(err);
    });
  });
}

async function editFileChannel(channel: string) {
  const file1Dir = `${projectDir}/capacitor.config.ts`;
  const file2Dir = `${androidDir}/app/src/main/java/com/g9game/app/MainActivity.java`;
  try {
    // 文件1: capacitor.config.ts
    let file1Content = await fs.readFile(file1Dir, 'utf-8');
    file1Content = file1Content.replace(
      /(const\s+channel\s*=\s*['"])[^'"]*(['"])/,
      `$1${channel}$2`,
    );
    await fs.writeFile(file1Dir, file1Content);

    // 文件2: MainActivity.java
    let file2Content = await fs.readFile(file2Dir, 'utf-8');
    file2Content = file2Content.replace(
      /(private\s+String\s+TopicName\s*=\s*["])[^"]*(["];)/,
      `$1${channel}$2`,
    );
    await fs.writeFile(file2Dir, file2Content);

    console.log(`渠道修改成功: ${channel}`);
  }
  catch (error) {
    console.error('修改渠道时出错:', error);
    throw error;
  }
}

main();
